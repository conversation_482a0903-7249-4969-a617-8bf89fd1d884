import { Page, Locator } from '@playwright/test';
import { ArbnbBasePage } from './arbnbBasePage';

export class SearchResultsPage extends ArbnbBasePage {
  readonly listingLinks: Locator;
  readonly mongKokApartmentLink: Locator;

  constructor(page: Page) {
    super(page);

    this.listingLinks = page.getByRole('link').filter({ hasText: 'דירה' });
    this.mongKokApartmentLink = page.getByRole('link', { name: 'דירה | מונג קוק' }).first();
  }
}
