import { Locator } from '@playwright/test';
import { BaseElement } from './baseElement';

export default class TextArea extends BaseElement {
  constructor(locator: Locator) {
    super(locator);
  }

  async fill({
    text,
    shouldClickBeforeWriting = true,
  }: {
    text: string;
    shouldClickBeforeWriting?: boolean;
  }) {
    if (shouldClickBeforeWriting) {
      await this.click();
    }

    await this.locator.fill(text);
  }

  async clear() {
    await this.locator.clear();
  }
}
