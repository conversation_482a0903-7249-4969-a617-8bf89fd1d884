import { Page, Locator } from '@playwright/test';

export class HomePage {
  // Locators (sorted alphabetically)
  readonly addGuestsButton: Locator;
  readonly checkInDateButton: Locator;
  readonly checkOutDateButton: Locator;
  readonly destinationInput: Locator;
  readonly hongKongOption: Locator;
  readonly searchButton: Locator;
  readonly stepperAdultsIncreaseButton: Locator;
  readonly stepperChildrenIncreaseButton: Locator;

  constructor(page: Page) {
    // Initialize locators (sorted alphabetically)
    this.addGuestsButton = page.getByRole('button', { name: 'מי הוספת אורחים' });
    this.checkInDateButton = page.getByRole('button', { name: '25, Wednesday, June 2025' });
    this.checkOutDateButton = page.getByRole('button', { name: '26, Thursday, June 2025' });
    this.destinationInput = page.getByTestId('structured-search-input-field-query');
    this.hongKongOption = page.getByRole('option', { name: 'Hong Kong' }).first();
    this.searchButton = page.getByTestId('structured-search-input-search-button');
    this.stepperAdultsIncreaseButton = page.getByTestId('stepper-adults-increase-button').first();
    this.stepperChildrenIncreaseButton = page
      .getByTestId('stepper-children-increase-button')
      .first();
  }
}
