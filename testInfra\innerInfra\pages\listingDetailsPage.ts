import { Page, Locator } from '@playwright/test';
import { ArbnbBasePage } from './arbnbBasePage';

export class ListingDetailsPage extends ArbnbBasePage {
  // Locators (sorted alphabetically)
  readonly availabilityCalendar: Locator;
  readonly bookingButton: Locator;
  readonly changeDatesButton: Locator;
  readonly checkInTextbox: Locator;
  readonly checkOutDateButton: Locator;
  readonly checkOutTextbox: Locator;
  readonly closeButton: Locator;
  readonly daySelector: Locator;
  readonly guestPickerDecreaseButton: Locator;
  readonly guestsButton: Locator;
  readonly nightHeading: Locator;
  readonly priceSelector: Locator;

  constructor(page: Page) {
    super(page);

    this.availabilityCalendar = page.getByTestId('bookit-sidebar-availability-calendar');
    this.bookingButton = page.getByRole('button', { name: 'הזמנה' });
    this.changeDatesButton = page.getByRole('button', { name: "שינוי התאריכים; צ'ק-אין: 2025" });
    this.checkInTextbox = page.getByRole('textbox', { name: "צ'ק-אין" });
    this.checkOutDateButton = page
      .getByRole('button', {
        name: "NaN, Invalid date, Invalid date NaN. זמין. יש לבחור בתור תאריך צ'ק-אאוט",
      })
      .nth(2);
    this.checkOutTextbox = page.getByRole('textbox', { name: "צ'ק-אאוט" });
    this.closeButton = page.getByRole('button', { name: 'סגירה' });
    this.daySelector = this.availabilityCalendar.locator('td').filter({ hasText: /^9$/ }).nth(2);
    this.guestPickerDecreaseButton = page.getByTestId(
      'GuestPicker-book_it-form-children-stepper-decrease-button'
    );
    this.guestsButton = page.getByRole('button', { name: '3 אורחים' });
    this.nightHeading = this.availabilityCalendar.getByRole('heading', { name: 'לילה' });
    this.priceSelector = page.locator('._cuv1re');
  }
}
