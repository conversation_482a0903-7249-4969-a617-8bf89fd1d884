import { expect, Locator } from '@playwright/test';

export abstract class BaseElement {
  protected locator: Locator;

  constructor(locator: Locator) {
    this.locator = locator;
  }

  // We will use click in base(despite not every element is clickable)
  // To simulate users more reliabily if needed(users will click on non clickable elements)
  // And to allow for easy removal of popups/modals and element focusing to reduce change of flakiness
  // ie: User can click on nav bar to close a popup
  async click() {
    await this.locator.click();
  }

  async isVisible({
    timeout = 5000,
    shouldThrow = false,
  }: { timeout?: number; shouldThrow?: boolean } = {}) {
    try {
      await expect(this.locator).toBeVisible({ timeout });

      return true;
    } catch (error) {
      if (shouldThrow) {
        throw error;
      }

      console.log(error.message);

      return false;
    }
  }
}
